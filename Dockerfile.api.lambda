# Base stage with shared environment
FROM node:22-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
WORKDIR /app
ENV AWS_REGION="me-south-1"
COPY package*.json ./
COPY pnpm-lock.yaml ./

# Install production dependencies
FROM base AS prod-deps
ENV AWS_REGION="me-south-1"
RUN pnpm install --prod --frozen-lockfile --ignore-scripts

# Build stage with full dependencies
FROM base AS build
COPY --from=prod-deps /app/node_modules ./node_modules
COPY nx.json ./
COPY tsconfig.base.json ./
COPY tsconfig.json ./
COPY jest.config.ts ./
COPY jest.preset.js ./
COPY eslint.config.mjs ./
COPY babel.config.json ./
COPY components.json ./
COPY libs/ libs/
COPY apps/api/ apps/api/
ENV AWS_REGION="me-south-1"
RUN pnpm install --frozen-lockfile
RUN pnpm nx build api

# Final production image
FROM base
WORKDIR /app
COPY --from=prod-deps /app/node_modules ./node_modules
COPY --from=build /app/dist/apps/api/ ./
ENV AWS_REGION="me-south-1"
EXPOSE 8080

# Add AWS Lambda Web Adapter
COPY --from=public.ecr.aws/awsguru/aws-lambda-adapter:0.8.2 /lambda-adapter /opt/extensions/lambda-adapter

CMD ["node", "main.js"]
