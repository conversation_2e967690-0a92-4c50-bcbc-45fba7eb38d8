import {
  integer,
  pgTable,
  text,
  boolean,
  timestamp,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { ulidField } from '../common-fields/ulid';
import { leads } from './leads';
import { WORKFLOW_STATES } from '../../constants/workflow-states';
import { timestampFields } from '../common-fields/timestamps';
import { WORKFLOW_STAGES } from '../../constants/workflow-stages';
import { sql } from 'drizzle-orm';

export const leadWorkflow = pgTable(
  'lead_workflow',
  {
    id: ulidField({ name: 'id', isPrimary: true }),
    leadId: text('lead_id')
      .references(() => leads.id, { onDelete: 'cascade' })
      .notNull(),
    status: text('status', {
      enum: Object.values(WORKFLOW_STATES) as [string, ...string[]],
    }),
    acceptanceLevel: integer('acceptance_level').default(0),
    currentStage: text('current_stage', {
      enum: Object.values(WORKFLOW_STAGES) as [string, ...string[]],
    }),
    isActive: boolean('is_active').default(true),
    isDeleted: boolean('is_deleted').default(false),
    pausedAt: timestamp('paused_at'),
    tenantId: text('tenant_id'),
    ...timestampFields,
  },
  (table) => [
    uniqueIndex('lead_workflow_unique_idx')
      .on(table.leadId)
      .where(sql`${table.isActive} = true`),
  ],
);

export type typeLeadWorkflow = typeof leadWorkflow.$inferSelect;
