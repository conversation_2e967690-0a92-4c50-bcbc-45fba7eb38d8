import { relations } from 'drizzle-orm';
import { developers, developerDescriptions, developerImages } from '../schema';
import { projects } from '../schema';

export const developerRelations = relations(developers, ({ many }) => ({
  descriptions: many(developerDescriptions),
  images: many(developerImages),
  projects: many(projects),
}));

export const developerDescriptionRelations = relations(
  developerDescriptions,
  ({ one }) => ({
    developer: one(developers, {
      fields: [developerDescriptions.developerId],
      references: [developers.id],
    }),
  }),
);
