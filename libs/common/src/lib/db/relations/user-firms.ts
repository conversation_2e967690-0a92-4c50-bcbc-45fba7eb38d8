import {
  leads,
  leadWorkflowAssignments,
  meetingUserFirm,
  userFirms,
  users,
  withdrawalRequests,
} from '../schema';

import { financialAccounts } from '../schema';
import { firms } from '../schema';
import { relations } from 'drizzle-orm';

export const userFirmsRelations = relations(userFirms, ({ one, many }) => ({
  user: one(users, {
    fields: [userFirms.userId],
    references: [users.id],
  }),
  firm: one(firms, {
    fields: [userFirms.firmId],
    references: [firms.id],
  }),
  financialAccount: one(financialAccounts, {
    fields: [userFirms.id],
    references: [financialAccounts.userFirmId],
  }),
  leads: many(leads),
  leadWorkflowAssignments: many(leadWorkflowAssignments),
  meetingUserFirms: many(meetingUserFirm),
  withdrawalRequests: many(withdrawalRequests),
}));
