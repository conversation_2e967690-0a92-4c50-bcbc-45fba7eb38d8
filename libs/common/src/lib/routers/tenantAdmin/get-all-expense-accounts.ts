import { tenantAdminProcedure } from './procedure';
import { and, eq } from 'drizzle-orm';
import { financialAccounts } from '../../db/schema';
import { FINANCIAL_ACCOUNT_TYPES } from '../../constants/financial-accounts-types';

export const getAllExpenseAccounts = tenantAdminProcedure.query(
  async ({ ctx }) => {
    const expenseAccounts = await ctx.db
      .select()
      .from(financialAccounts)
      .where(
        and(
          eq(
            financialAccounts.category,
            FINANCIAL_ACCOUNT_TYPES.TENANT_INTERNAL_EXPENSES,
          ),
          eq(financialAccounts.firmId, ctx.tenantId),
        ),
      );
    return expenseAccounts;
  },
);
