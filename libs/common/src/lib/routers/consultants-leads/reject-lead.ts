import { leads, leadWorkflow, leadWorkflowAssignments } from '../../db/schema';

import { consultantLeadsProcedure } from './procedure';
import {
  LEAD_ASSIGNMENT_STATUS,
  LeadAssignmentStatus,
} from '../../constants/lead-assignment-status';
import { and, eq } from 'drizzle-orm';
import { TRPCError } from '@trpc/server';
import { InferSelectModel } from 'drizzle-orm';
import { getLeadStatusAfterChange } from '../../utils/get-lead-status-after-change';
import { consultantLeadSchema } from '../../zod-schema/consultant-lead-schema';

/**
 * Define the acceptLead mutation procedure.
 * This procedure allows a consultant to accept a lead that was assigned to them.
 */
export const rejectLead = consultantLeadsProcedure
  // Validate the input using the consultant lead schema
  .input(consultantLeadSchema)
  .mutation(async ({ ctx, input }) => {
    // Execute all database operations in a transaction to ensure atomicity
    return await ctx.db.transaction(async (tx) => {
      // Step 1: Update the assignment status to ACCEPTED for this specific consultant
      await tx
        .update(leadWorkflowAssignments)
        .set({
          status: LEAD_ASSIGNMENT_STATUS.REJECTED,
        })
        .where(
          and(
            eq(leadWorkflowAssignments.workflowId, input.leadWorkflowId),
            eq(leadWorkflowAssignments.userFirmId, ctx.userFirmId as string),
          ),
        )
        .returning();

      // Step 2: Query the current lead workflow to get up-to-date information
      const queryThisLeadWorkFlow = (await tx.query.leadWorkflow.findFirst({
        where: and(
          eq(leadWorkflow.id, input.leadWorkflowId),
          eq(leadWorkflow.leadId, input.leadId),
        ),
        with: {
          leadWorkflowAssignments: true,
        },
      })) as unknown as InferSelectModel<typeof leadWorkflow> & {
        leadWorkflowAssignments: InferSelectModel<
          typeof leadWorkflowAssignments
        >[];
      };

      // Verify that the lead workflow exists
      if (!queryThisLeadWorkFlow) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Lead workflow not found',
        });
      }

      // Step 3: Calculate the current state of assignments for this lead workflow
      // This counts how many assignments are in each status category
      const currentState = {
        // Number of acceptances required for this workflow
        requiredAcceptances: queryThisLeadWorkFlow.acceptanceLevel ?? 0,
        // Count of consultants in INVITED status (+1 because we already changed this user's status)
        invited:
          queryThisLeadWorkFlow.leadWorkflowAssignments.filter(
            (a) => a.status === LEAD_ASSIGNMENT_STATUS.INVITED,
          ).length + 1,
        // Count of consultants in ACCEPTED status (-1 because we already changed this user's status)
        accepted:
          queryThisLeadWorkFlow.leadWorkflowAssignments.filter(
            (a) => a.status === LEAD_ASSIGNMENT_STATUS.ACCEPTED,
          ).length - 1,
        // Count of consultants in REJECTED status
        rejected:
          queryThisLeadWorkFlow.leadWorkflowAssignments.filter(
            (a) => a.status === LEAD_ASSIGNMENT_STATUS.REJECTED,
          ).length ?? 0,
        // Count of consultants in CANCELLED status
        cancelled:
          queryThisLeadWorkFlow.leadWorkflowAssignments.filter(
            (a) => a.status === LEAD_ASSIGNMENT_STATUS.CANCELLED,
          ).length ?? 0,
        // Count of consultants in EXPIRED status
        expired:
          queryThisLeadWorkFlow.leadWorkflowAssignments.filter(
            (a) => a.status === LEAD_ASSIGNMENT_STATUS.EXPIRED,
          ).length ?? 0,
        // Count of consultants in DROPPED_OUT status
        dropped_out:
          queryThisLeadWorkFlow.leadWorkflowAssignments.filter(
            (a) => a.status === LEAD_ASSIGNMENT_STATUS.DROPPED_OUT,
          ).length ?? 0,
      };

      // Step 4: Determine the new lead status based on the status change and current state
      const { leadStatus } = getLeadStatusAfterChange(currentState, {
        fromStatus: queryThisLeadWorkFlow.status as LeadAssignmentStatus,
        toStatus: LEAD_ASSIGNMENT_STATUS.REJECTED,
      });

      // Step 5: Update the lead workflow status with the calculated sub-status
      await tx
        .update(leadWorkflow)
        .set({
          status: leadStatus.subStatus,
        })
        .where(eq(leadWorkflow.id, input.leadWorkflowId));

      // Step 6: Update the lead's main status
      await tx
        .update(leads)
        .set({
          status: leadStatus.status,
        })
        .where(eq(leads.id, queryThisLeadWorkFlow.leadId));

      // Step 7: Return success message to the client
      return {
        message: 'Lead rejected',
      };
    });
  });
