import { and } from 'drizzle-orm';
import { eq } from 'drizzle-orm';
import { leads, leadWorkflow, workflowStages } from '../../db/schema';
import { consultantLeadSchema } from '../../zod-schema/consultant-lead-schema';
import { consultantLeadsProcedure } from './procedure';
import { WORKFLOW_STATES } from '../../constants/workflow-states';
import { z } from 'zod';
export const getInitialMeetingDetails = consultantLeadsProcedure

  .input(
    consultantLeadSchema.extend({
      sequence: z.number(),
      stageName: z.string(),
    }),
  )
  .query(async ({ ctx, input }) => {
    console.log(`026737856508146033 `, input);
    const lead = await ctx.db.query.leads.findFirst({
      where: and(eq(leads.id, input.leadId), eq(leads.firmId, ctx.tenantId)),
      with: {
        leadWorkflow: {
          where: and(
            eq(leadWorkflow.leadId, input.leadId),
            eq(leadWorkflow.id, input.leadWorkflowId),
            eq(leadWorkflow.status, WORKFLOW_STATES.ACTIVE),
            eq(leadWorkflow.isActive, true),
            eq(leadWorkflow.isDeleted, false),
          ),
          with: {
            workflowStages: {
              where: and(
                eq(workflowStages.stage_type, input.stageName),
                eq(workflowStages.sort_order, input.sequence),
              ),
              with: {
                meeting: true,
              },
            },
          },
        },
      },
    });

    return { lead };
  });
