import { consultantLeadsProcedure } from './procedure';
import { consultantLeadSchema } from '../../zod-schema/consultant-lead-schema';
import { and, inArray } from 'drizzle-orm';
import { eq } from 'drizzle-orm';
import { MEETING_RESULT, MEETING_STATUS } from '../../constants/meeting-status';
import {
  leadWorkflow,
  leadWorkflowAssignments,
  meeting,
  workflowStages,
} from '../../db';
import { TRPCError } from '@trpc/server';
import { LEAD_ASSIGNMENT_STATUS } from '../../constants/lead-assignment-status';
import { WORKFLOW_STATES } from '../../constants/workflow-states';
import { WORKFLOW_STAGES } from '../../constants/workflow-stages';

export const qualifyLeads = consultantLeadsProcedure
  .input(consultantLeadSchema)
  .mutation(async ({ ctx, input }) => {
    try {
      const findAllPendingMeeting = await ctx.db.query.leadWorkflow.findFirst({
        where: and(
          eq(leadWorkflow.id, input.leadWorkflowId),
          eq(leadWorkflow.leadId, input.leadId),
          eq(leadWorkflow.status, WORKFLOW_STATES.ACTIVE),
          eq(leadWorkflow.isActive, true),
          eq(leadWorkflow.isDeleted, false),
        ),
        with: {
          leadWorkflowAssignments: {
            where: and(
              eq(
                leadWorkflowAssignments.status,
                LEAD_ASSIGNMENT_STATUS.ACCEPTED,
              ),
            ),
          },
          workflowStages: {
            where: and(
              eq(workflowStages.stage_type, WORKFLOW_STAGES.INITIAL_MEETING),
            ),
            with: {
              meeting: {
                where: and(eq(meeting.status, MEETING_STATUS.PENDING)),
              },
            },
          },
        },
      });

      const allPendingMeetings = findAllPendingMeeting?.workflowStages
        .flatMap((stage) => stage.meeting ?? [])
        .filter(
          (meeting): meeting is NonNullable<typeof meeting> => meeting != null,
        );

      const allWorkflowStageIds =
        findAllPendingMeeting?.workflowStages?.map((stage) => stage.id) ?? [];

      if (!allPendingMeetings || allPendingMeetings.length === 0) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'No pending meetings found',
        });
      }

      await ctx.db.transaction(async (tx) => {
        await tx
          .update(leadWorkflow)
          .set({
            currentStage: WORKFLOW_STAGES.INVESTMENT_DETAILS,
          })
          .where(eq(leadWorkflow.id, input.leadWorkflowId));

        await tx
          .update(workflowStages)
          .set({
            completed_at: new Date(),
          })
          .where(inArray(workflowStages.id, allWorkflowStageIds));

        await tx.insert(workflowStages).values({
          workflowId: input.leadWorkflowId,
          stage_type: WORKFLOW_STAGES.QUALIFICATION,
          completed_at: new Date(),
          sort_order: 2,
        });
        await tx.insert(workflowStages).values({
          workflowId: input.leadWorkflowId,
          stage_type: WORKFLOW_STAGES.INVESTMENT_DETAILS,
          sort_order: 3,
        });

        await tx
          .update(meeting)
          .set({
            status: MEETING_STATUS.COMPLETED,
            result: MEETING_RESULT.SHOW_UP,
          })
          .where(
            inArray(
              meeting.id,
              allPendingMeetings.map((meeting) => meeting.id),
            ),
          );
      });
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message:
          error instanceof Error
            ? error.message
            : 'Failed to move lead to no show',
        cause: error,
      });
    }
  });
