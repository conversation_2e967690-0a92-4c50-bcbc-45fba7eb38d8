// import { tenantAdminRouter } from './tenantAdmin';

import { router } from './router';
import { consultantRouter } from './routers/consultant';
import { protectedRouter } from './routers/protected';
import tenantAdminRouter from './routers/tenantAdmin';
import { consultantLeadsRouter } from './routers/consultants-leads';
import { consultantBookingRouter } from './routers/consultant-booking';
export const appRouter = router({
  protected: protectedRouter,
  tenantAdmin: tenantAdminRouter,
  consultant: consultantRouter,
  consultantLeads: consultant<PERSON><PERSON>sRouter,
  consultantBooking: consultantBookingRouter,
});

export type AppRouter = typeof appRouter;
