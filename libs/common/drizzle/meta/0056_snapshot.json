{"id": "df2cfbb4-d09e-40df-9983-c8c9b957e77e", "prevId": "d4704ef5-80d6-40c9-8a4d-d4b49ac7556e", "version": "7", "dialect": "postgresql", "tables": {"public.user_firm_bank_details": {"name": "user_firm_bank_details", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "user_firm_id": {"name": "user_firm_id", "type": "text", "primaryKey": false, "notNull": false}, "firm_id": {"name": "firm_id", "type": "text", "primaryKey": false, "notNull": true}, "bank_details": {"name": "bank_details", "type": "jsonb", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_firm_bank_details_user_firm_id_user_firms_id_fk": {"name": "user_firm_bank_details_user_firm_id_user_firms_id_fk", "tableFrom": "user_firm_bank_details", "tableTo": "user_firms", "columnsFrom": ["user_firm_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_firm_bank_details_firm_id_firms_id_fk": {"name": "user_firm_bank_details_firm_id_firms_id_fk", "tableFrom": "user_firm_bank_details", "tableTo": "firms", "columnsFrom": ["firm_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_firm_bank_details_user_firm_id_unique": {"name": "user_firm_bank_details_user_firm_id_unique", "nullsNotDistinct": false, "columns": ["user_firm_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.financial_accounts": {"name": "financial_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "text", "primaryKey": false, "notNull": false}, "initial_balance": {"name": "initial_balance", "type": "numeric", "primaryKey": false, "notNull": true}, "current_balance": {"name": "current_balance", "type": "numeric", "primaryKey": false, "notNull": true}, "user_firm_id": {"name": "user_firm_id", "type": "text", "primaryKey": false, "notNull": false}, "firm_id": {"name": "firm_id", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"financial_accounts_parent_id_financial_accounts_id_fk": {"name": "financial_accounts_parent_id_financial_accounts_id_fk", "tableFrom": "financial_accounts", "tableTo": "financial_accounts", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "financial_accounts_user_firm_id_user_firms_id_fk": {"name": "financial_accounts_user_firm_id_user_firms_id_fk", "tableFrom": "financial_accounts", "tableTo": "user_firms", "columnsFrom": ["user_firm_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "financial_accounts_firm_id_firms_id_fk": {"name": "financial_accounts_firm_id_firms_id_fk", "tableFrom": "financial_accounts", "tableTo": "firms", "columnsFrom": ["firm_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"financial_accounts_user_firm_id_unique": {"name": "financial_accounts_user_firm_id_unique", "nullsNotDistinct": false, "columns": ["user_firm_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "address_line1": {"name": "address_line1", "type": "text", "primaryKey": false, "notNull": true}, "address_line2": {"name": "address_line2", "type": "text", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "postal_code": {"name": "postal_code", "type": "text", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"addresses_user_id_users_id_fk": {"name": "addresses_user_id_users_id_fk", "tableFrom": "addresses", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"addresses_user_id_unique": {"name": "addresses_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.consultants": {"name": "consultants", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"consultants_user_id_users_id_fk": {"name": "consultants_user_id_users_id_fk", "tableFrom": "consultants", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.firms": {"name": "firms", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "clerk_org_id": {"name": "clerk_org_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"firms_clerk_org_id_unique": {"name": "firms_clerk_org_id_unique", "nullsNotDistinct": false, "columns": ["clerk_org_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_firms": {"name": "user_firms", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "firm_id": {"name": "firm_id", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"active_user_idx": {"name": "active_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"user_firms\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}, "user_firm_unique_idx": {"name": "user_firm_unique_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "firm_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_firms_user_id_users_id_fk": {"name": "user_firms_user_id_users_id_fk", "tableFrom": "user_firms", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_firms_firm_id_firms_id_fk": {"name": "user_firms_firm_id_firms_id_fk", "tableFrom": "user_firms", "tableTo": "firms", "columnsFrom": ["firm_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "clerk_user_id": {"name": "clerk_user_id", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(201)", "primaryKey": false, "notNull": false, "generated": {"as": "COALESCE(\"users\".\"first_name\", '') || ' ' || COALESCE(\"users\".\"last_name\", '')", "type": "stored"}}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "avatar_key": {"name": "avatar_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_clerk_user_id_unique": {"name": "users_clerk_user_id_unique", "nullsNotDistinct": false, "columns": ["clerk_user_id"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_phone_number_unique": {"name": "users_phone_number_unique", "nullsNotDistinct": false, "columns": ["phone_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.leads": {"name": "leads", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "client_name": {"name": "client_name", "type": "text", "primaryKey": false, "notNull": true}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "nationality_code": {"name": "nationality_code", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": true}, "nationality_name": {"name": "nationality_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "primary_phone_number": {"name": "primary_phone_number", "type": "jsonb", "primaryKey": false, "notNull": false}, "other_phone_numbers": {"name": "other_phone_numbers", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "firm_id": {"name": "firm_id", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'new'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"leads_created_by_user_firms_id_fk": {"name": "leads_created_by_user_firms_id_fk", "tableFrom": "leads", "tableTo": "user_firms", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "leads_firm_id_firms_id_fk": {"name": "leads_firm_id_firms_id_fk", "tableFrom": "leads", "tableTo": "firms", "columnsFrom": ["firm_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lead_generation_fields": {"name": "lead_generation_fields", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "firm_id": {"name": "firm_id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "field_reference": {"name": "field_reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_required": {"name": "is_required", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"lead_generation_fields_firm_id_firms_id_fk": {"name": "lead_generation_fields_firm_id_firms_id_fk", "tableFrom": "lead_generation_fields", "tableTo": "firms", "columnsFrom": ["firm_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "lead_generation_fields_created_by_user_firms_id_fk": {"name": "lead_generation_fields_created_by_user_firms_id_fk", "tableFrom": "lead_generation_fields", "tableTo": "user_firms", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "lead_generation_fields_updated_by_user_firms_id_fk": {"name": "lead_generation_fields_updated_by_user_firms_id_fk", "tableFrom": "lead_generation_fields", "tableTo": "user_firms", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"lead_generation_fields_field_reference_firm_id_unique": {"name": "lead_generation_fields_field_reference_firm_id_unique", "nullsNotDistinct": false, "columns": ["field_reference", "firm_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lead_generation_details": {"name": "lead_generation_details", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "lead_id": {"name": "lead_id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "text_field_1": {"name": "text_field_1", "type": "text", "primaryKey": false, "notNull": false}, "text_field_2": {"name": "text_field_2", "type": "text", "primaryKey": false, "notNull": false}, "text_field_3": {"name": "text_field_3", "type": "text", "primaryKey": false, "notNull": false}, "text_field_4": {"name": "text_field_4", "type": "text", "primaryKey": false, "notNull": false}, "text_field_5": {"name": "text_field_5", "type": "text", "primaryKey": false, "notNull": false}, "text_field_6": {"name": "text_field_6", "type": "text", "primaryKey": false, "notNull": false}, "text_field_7": {"name": "text_field_7", "type": "text", "primaryKey": false, "notNull": false}, "text_field_8": {"name": "text_field_8", "type": "text", "primaryKey": false, "notNull": false}, "text_field_9": {"name": "text_field_9", "type": "text", "primaryKey": false, "notNull": false}, "text_field_10": {"name": "text_field_10", "type": "text", "primaryKey": false, "notNull": false}, "text_field_11": {"name": "text_field_11", "type": "text", "primaryKey": false, "notNull": false}, "text_field_12": {"name": "text_field_12", "type": "text", "primaryKey": false, "notNull": false}, "text_field_13": {"name": "text_field_13", "type": "text", "primaryKey": false, "notNull": false}, "text_field_14": {"name": "text_field_14", "type": "text", "primaryKey": false, "notNull": false}, "text_field_15": {"name": "text_field_15", "type": "text", "primaryKey": false, "notNull": false}, "date_field_1": {"name": "date_field_1", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "date_field_2": {"name": "date_field_2", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "date_field_3": {"name": "date_field_3", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "date_field_4": {"name": "date_field_4", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "date_field_5": {"name": "date_field_5", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "date_field_6": {"name": "date_field_6", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "date_field_7": {"name": "date_field_7", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"lead_generation_details_lead_id_leads_id_fk": {"name": "lead_generation_details_lead_id_leads_id_fk", "tableFrom": "lead_generation_details", "tableTo": "leads", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"lead_generation_details_lead_id_unique": {"name": "lead_generation_details_lead_id_unique", "nullsNotDistinct": false, "columns": ["lead_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.requirement_brief": {"name": "requirement_brief", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "lead_id": {"name": "lead_id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "purpose": {"name": "purpose", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'::text[]"}, "start_budget": {"name": "start_budget", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "end_budget": {"name": "end_budget", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true}, "property_type": {"name": "property_type", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'::text[]"}, "size": {"name": "size", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "size_unit": {"name": "size_unit", "type": "text", "primaryKey": false, "notNull": true}, "ready_offplan": {"name": "ready_offplan", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'::text[]"}, "is_mortgage": {"name": "is_mortgage", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_other_agency_engaged": {"name": "is_other_agency_engaged", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"requirement_brief_lead_id_leads_id_fk": {"name": "requirement_brief_lead_id_leads_id_fk", "tableFrom": "requirement_brief", "tableTo": "leads", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "requirement_brief_created_by_user_firms_id_fk": {"name": "requirement_brief_created_by_user_firms_id_fk", "tableFrom": "requirement_brief", "tableTo": "user_firms", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "requirement_brief_updated_by_user_firms_id_fk": {"name": "requirement_brief_updated_by_user_firms_id_fk", "tableFrom": "requirement_brief", "tableTo": "user_firms", "columnsFrom": ["updated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"requirement_brief_lead_id_unique": {"name": "requirement_brief_lead_id_unique", "nullsNotDistinct": false, "columns": ["lead_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lead_workflow": {"name": "lead_workflow", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "lead_id": {"name": "lead_id", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "acceptance_level": {"name": "acceptance_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "current_stage": {"name": "current_stage", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "paused_at": {"name": "paused_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"lead_workflow_unique_idx": {"name": "lead_workflow_unique_idx", "columns": [{"expression": "lead_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"lead_workflow\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"lead_workflow_lead_id_leads_id_fk": {"name": "lead_workflow_lead_id_leads_id_fk", "tableFrom": "lead_workflow", "tableTo": "leads", "columnsFrom": ["lead_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.lead_workflow_assignments": {"name": "lead_workflow_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": false}, "user_firm_id": {"name": "user_firm_id", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "accepted_at": {"name": "accepted_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "left_at": {"name": "left_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "leave_reason": {"name": "leave_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"lead_workflow_assignment_unique_idx": {"name": "lead_workflow_assignment_unique_idx", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_firm_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"lead_workflow_assignments_workflow_id_lead_workflow_id_fk": {"name": "lead_workflow_assignments_workflow_id_lead_workflow_id_fk", "tableFrom": "lead_workflow_assignments", "tableTo": "lead_workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "lead_workflow_assignments_user_firm_id_user_firms_id_fk": {"name": "lead_workflow_assignments_user_firm_id_user_firms_id_fk", "tableFrom": "lead_workflow_assignments", "tableTo": "user_firms", "columnsFrom": ["user_firm_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "lead_workflow_assignments_created_by_user_firms_id_fk": {"name": "lead_workflow_assignments_created_by_user_firms_id_fk", "tableFrom": "lead_workflow_assignments", "tableTo": "user_firms", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.workflow_stages": {"name": "workflow_stages", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "workflow_id": {"name": "workflow_id", "type": "text", "primaryKey": false, "notNull": false}, "stage_type": {"name": "stage_type", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "stage_data": {"name": "stage_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"workflow_stage_unique_index": {"name": "workflow_stage_unique_index", "columns": [{"expression": "workflow_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "stage_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"workflow_stages_workflow_id_lead_workflow_id_fk": {"name": "workflow_stages_workflow_id_lead_workflow_id_fk", "tableFrom": "workflow_stages", "tableTo": "lead_workflow", "columnsFrom": ["workflow_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.meeting": {"name": "meeting", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": true, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "is_deleted": {"name": "is_deleted", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "result": {"name": "result", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "meeting_date": {"name": "meeting_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "meeting_url": {"name": "meeting_url", "type": "text", "primaryKey": false, "notNull": false}, "meeting_password": {"name": "meeting_password", "type": "text", "primaryKey": false, "notNull": false}, "meeting_location": {"name": "meeting_location", "type": "jsonb", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "workflow_stage": {"name": "workflow_stage", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"meeting_tenant_id_firms_id_fk": {"name": "meeting_tenant_id_firms_id_fk", "tableFrom": "meeting", "tableTo": "firms", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "meeting_workflow_stage_workflow_stages_id_fk": {"name": "meeting_workflow_stage_workflow_stages_id_fk", "tableFrom": "meeting", "tableTo": "workflow_stages", "columnsFrom": ["workflow_stage"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.meeting_userfirm": {"name": "meeting_userfirm", "schema": "", "columns": {"meeting_id": {"name": "meeting_id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "user_firm_id": {"name": "user_firm_id", "type": "<PERSON><PERSON><PERSON>(26)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"meeting_userfirm_meeting_id_meeting_id_fk": {"name": "meeting_userfirm_meeting_id_meeting_id_fk", "tableFrom": "meeting_userfirm", "tableTo": "meeting", "columnsFrom": ["meeting_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "meeting_userfirm_user_firm_id_user_firms_id_fk": {"name": "meeting_userfirm_user_firm_id_user_firms_id_fk", "tableFrom": "meeting_userfirm", "tableTo": "user_firms", "columnsFrom": ["user_firm_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"meeting_userfirm_meeting_id_user_firm_id_pk": {"name": "meeting_userfirm_meeting_id_user_firm_id_pk", "columns": ["meeting_id", "user_firm_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}