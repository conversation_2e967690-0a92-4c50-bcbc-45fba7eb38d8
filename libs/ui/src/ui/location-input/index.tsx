'use client';

import * as React from 'react';
import { CountrySelect } from './country-select';
import { StateSelect } from './state-select';
import { CitySelect } from './city-select';
import type { LocationInputProps, LocationChangeParams } from './types';
import type { ICountry, IState, ICity } from 'country-state-city';

export function LocationInput({ onLocationChange }: LocationInputProps) {
  const [selectedCountry, setSelectedCountry] = React.useState<ICountry | null>(
    null,
  );
  const [selectedState, setSelectedState] = React.useState<IState | null>(null);
  const [selectedCity, setSelectedCity] = React.useState<ICity | null>(null);

  const handleCountryChange = (country: ICountry) => {
    setSelectedCountry(country);
    setSelectedState(null);
    setSelectedCity(null);
    onLocationChange?.({ country, state: null, city: null });
  };

  const handleStateChange = (state: IState) => {
    setSelectedState(state);
    setSelectedCity(null);
    onLocationChange?.({ country: selectedCountry, state, city: null });
  };

  const handleCityChange = (city: ICity) => {
    setSelectedCity(city);
    onLocationChange?.({
      country: selectedCountry,
      state: selectedState,
      city,
    });
  };

  return (
    <div className="flex flex-col gap-2">
      <CountrySelect
        selectedCountry={selectedCountry}
        onSelect={handleCountryChange}
      />
      <StateSelect
        selectedCountry={selectedCountry}
        selectedState={selectedState}
        onSelect={handleStateChange}
      />
      <CitySelect
        selectedCountry={selectedCountry}
        selectedState={selectedState}
        selectedCity={selectedCity}
        onSelect={handleCityChange}
      />
    </div>
  );
}

export type { LocationInputProps, LocationChangeParams };
