# Use official Nginx Alpine base image
FROM nginx:alpine

# Copy Lambda adapter
COPY --from=public.ecr.aws/awsguru/aws-lambda-adapter:0.8.2 /lambda-adapter /opt/extensions/lambda-adapter

# Create /tmp directories for Nginx
RUN mkdir -p /tmp/client_temp /tmp/proxy_temp /tmp/fastcgi_temp /tmp/uwsgi_temp /tmp/scgi_temp && \
    chown -R nginx:nginx /tmp && \
    chmod -R 777 /tmp

# Create a template for nginx.conf
RUN printf "events {}\n\
http {\n\
    client_body_temp_path /tmp/client_temp;\n\
    proxy_temp_path /tmp/proxy_temp;\n\
    fastcgi_temp_path /tmp/fastcgi_temp;\n\
    uwsgi_temp_path /tmp/uwsgi_temp;\n\
    scgi_temp_path /tmp/scgi_temp;\n\
\n\
    server {\n\
        listen 8080;\n\
        location / {\n\
            return 302 %s;\n\
        }\n\
    }\n\
}\n" > /etc/nginx/nginx.conf.template

# Remove default entrypoint scripts to avoid conflicts
RUN rm -rf /docker-entrypoint.d/*

# Set environment variable for redirect URL (default for testing)
ENV REDIRECT_URL=https://electric-prawn-2.accounts.dev/sign-in?redirect_url=https%3A%2F%2F<api-id>.execute-api.me-south-1.amazonaws.com%2Fprod%2F

# Override entrypoint to substitute REDIRECT_URL into nginx.conf
COPY --chmod=755 entrypoint.sh /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]

# Expose port 8080
EXPOSE 8080

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]