# name: CI

# on:
#   push:
#     branches:
#       - '**'
#   pull_request:
#     branches:
#       - '**'

# permissions:
#   actions: read
#   contents: read

# jobs:
#   setup:
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v4
#         with:
#           fetch-depth: 0
#       - name: Install pnpm
#         run: npm install -g pnpm
#       - name: Install dependencies
#         run: pnpm i
#       - name: Cache dependencies
#         uses: actions/cache@v3
#         with:
#           path: |
#             node_modules
#             ~/.pnpm-store
#           key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
#           restore-keys: |
#             ${{ runner.os }}-pnpm-

#   lint:
#     needs: setup
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v4
#       - name: Install pnpm
#         run: npm install -g pnpm
#       - name: Restore dependencies
#         uses: actions/cache@v3
#         with:
#           path: |
#             node_modules
#             ~/.pnpm-store
#           key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
#       - name: Lint
#         run: pnpm lint

#   format:
#     needs: setup
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v4
#       - name: Install pnpm
#         run: npm install -g pnpm
#       - name: Restore dependencies
#         uses: actions/cache@v3
#         with:
#           path: |
#             node_modules
#             ~/.pnpm-store
#           key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
#       - name: Check Prettier formatting
#         run: pnpm format_prettier

#   build:
#     needs: setup
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v4
#       - name: Install pnpm
#         run: npm install -g pnpm
#       - name: Restore dependencies
#         uses: actions/cache@v3
#         with:
#           path: |
#             node_modules
#             ~/.pnpm-store
#           key: ${{ runner.os }}-pnpm-${{ hashFiles('**/pnpm-lock.yaml') }}
#       - name: Build NextJS app
#         run: API_URL=https://prop-stack.raynatours.com/api/trpc pnpm nx run web:build