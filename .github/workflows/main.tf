terraform {

  backend "s3" {}

  required_version = ">= 1.0.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 3.19"
    }
    docker = {
      source  = "kreuzwerker/docker"
      version = ">= 2.12"
    }
  }
}

locals {
  ns = "${var.name}-${var.environment}"
}

data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

data "aws_ecr_authorization_token" "token" {}

provider "aws" {}

provider "docker" {
  registry_auth {
    address  = format("%v.dkr.ecr.%v.amazonaws.com", data.aws_caller_identity.current.account_id, data.aws_region.current.name)
    username = data.aws_ecr_authorization_token.token.user_name
    password = data.aws_ecr_authorization_token.token.password
  }
}

# resource "aws_security_group" "lambda_sg_api" {
#   name        = "${local.ns}-sg-api"
#   description = "Security group for API Lambda function"
#   vpc_id      = var.vpc_id

#   ingress {
#     description = "Allow HTTP"
#     from_port   = 0
#     to_port     = 0
#     protocol    = "-1"
#     cidr_blocks = ["0.0.0.0/0"]
#   }

#   egress {
#     description = "Allow all outbound traffic"
#     from_port   = 0
#     to_port     = 0
#     protocol    = "-1"
#     cidr_blocks = ["0.0.0.0/0"]
#   }
# }

resource "aws_security_group" "lambda_sg_web" {
  name        = "${local.ns}-sg-web"
  description = "Security group for web lambda function"
  vpc_id      = var.vpc_id

  ingress {
    description = "Allow HTTP"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}



# module "docker_image_api" {
#   source = "terraform-aws-modules/lambda/aws//modules/docker-build"

#   create_ecr_repo  = true
#   ecr_repo         = "${local.ns}-api"
#   image_tag        = var.image_tag_api
#   source_path      = "../../"
#   docker_file_path = "Dockerfile.api.lambda"
# }

module "docker_image_web" {
  source = "terraform-aws-modules/lambda/aws//modules/docker-build"

  create_ecr_repo  = true
  ecr_repo         = "${local.ns}-web"
  image_tag        = var.image_tag_web
  source_path      = "../../devops"
  docker_file_path = "Dockerfile"
 # depends_on       = [module.docker_image_api]
}

# module "lambda_function_from_container_image_api" {
#   source = "terraform-aws-modules/lambda/aws"

#   function_name              = "${local.ns}-api"
#   description                = "Ephemeral preview environment for: ${local.ns}"
#   create_package             = false
#   package_type               = "Image"
#   image_uri                  = module.docker_image_api.image_uri
#   architectures              = ["x86_64"]
#   create_lambda_function_url = true
#   lambda_role                = "${local.ns}-api-role"
#   logging_log_group          = "/aws/lambda/api/${local.ns}"
#   timeout                    = 300
#   memory_size                = 512
#   vpc_subnet_ids             = ["subnet-03a6dbfc45c7249fe", "subnet-0881ab2d79c77b4ed"]
#   vpc_security_group_ids     = [aws_security_group.lambda_sg_api.id]
#   environment_variables = {
#     "NODE_ENV"              = "dev",
#     "API_PORT"              = "8080"
#     "PORT"                  = "8080"
#     "AWS_BUCKET_NAME"       = "s3"
#     "CLERK_DEBUG"           = true
#     "CLERK_PUBLISHABLE_KEY" = var.cleark_publishable_key
#     "CLERK_SECRET_KEY"      = var.clear_secret_key
#     "DB_HOST"         = var.database_host
#     "DB_NAME"         = "rayna_properties"
#     "DB_PORT"         = "5432"
#     "DB_USER"          = "dbadminusr"
#     "DB_PASSWORD"          = var.database_password
#     "MAPBOX_ACCESS_TOKEN"   = var.mapbox_access_token
#     "REDIS_URL"             = var.redis_url
#     "TRPC_PATH"             = "api/trpc"


#   }
#   depends_on = [module.docker_image_api]
# }

module "lambda_function_from_container_image_web" {
  source = "terraform-aws-modules/lambda/aws"

  function_name                     = "${local.ns}-web"
  description                       = "Ephemeral preview environment for: ${local.ns}"
  create_package                    = false
  package_type                      = "Image"
  image_uri                         = module.docker_image_web.image_uri
  architectures                     = ["x86_64"]
  create_lambda_function_url        = true
  lambda_role                       = "${local.ns}-web-role"
  logging_log_group                 = "/aws/lambda/web/${local.ns}"
  cloudwatch_logs_retention_in_days = 7
  timeout                           = 600
  memory_size                       = 2048
  # vpc_subnet_ids                    = ["subnet-03a6dbfc45c7249fe", "subnet-0881ab2d79c77b4ed"]
  # vpc_security_group_ids            = [aws_security_group.lambda_sg_web.id]
  environment_variables = {
    # "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY" = var.cleark_publishable_key
    # "CLERK_PUBLISHABLE_KEY"             = var.cleark_publishable_key
    "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"="pk_test_aW4taHVza3ktNS5jbGVyay5hY2NvdW50cy5kZXYk"
    "CLERK_SECRET_KEY"="sk_test_lhl6aAlB7oKo7CBdyWKzLjeihMeh6KB4dtAyAI0Wif"
    # "CLERK_SECRET_KEY"                  = var.clear_secret_key
    # "API_URL"                           = ""
    # "NEXT_PUBLIC_API_URL"               = "https://prop-stack.raynatours.com/api/trpc"
    # "REDIS_URL"                         = var.redis_url
    # "NEXT_PUBLIC_ASSET_PREFIX"          = "s3"
  }
  depends_on = [module.docker_image_web]
}

# output "endpoint_url_api" {
#   value = module.lambda_function_from_container_image_api.lambda_function_url
# }

output "endpoint_url_web" {
  value = module.lambda_function_from_container_image_web.lambda_function_url
}

# iam roles update
# resource "aws_iam_role_policy" "inline_policy_api" {
#   name = "vpc-access"
#   role = module.lambda_function_from_container_image_api.lambda_role_name

#   policy = jsonencode({
#     Version = "2012-10-17",
#     Statement = [
#       {
#         Effect = "Allow",
#         Action = [
#           "ec2:CreateNetworkInterface",
#           "ec2:DescribeNetworkInterfaces",
#           "ec2:DeleteNetworkInterface"
#         ],
#         Resource = "*"
#       },
#     ]
#   })
# }


resource "aws_iam_role_policy" "inline_policy_web" {
  name = "vpc-access"
  role = module.lambda_function_from_container_image_web.lambda_role_name

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface"
        ],
        Resource = "*"
      },
    ]
  })
}