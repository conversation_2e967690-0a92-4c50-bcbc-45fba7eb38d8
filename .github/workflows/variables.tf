variable "vpc_id" {
  type        = string
  description = "VPC ID for the resources"
  default     = "vpc-081aa2d6f6e9c1c4c"
}

variable "name" {
  type        = string
  description = "name for the resources"
}

variable "environment" {
  type        = string
  description = "environment for the resources"
}

variable "image_tag_api" {
  type        = string
  description = "container image tag"
}

variable "image_tag_web" {
  type        = string
  description = "container image tag"
}

variable "database_host" {
  type        = string
  description = "Database host for the application"

}

variable "database_password" {
  type        = string
  description = "Database password for the application"

}

variable "redis_url" {
  type        = string
  description = "Redis URL for the application"

}

variable "cleark_publishable_key" {
  type        = string
  description = "Clerk publishable key for the application"

}

variable "clear_secret_key" {
  type        = string
  description = "Clerk secret key for the application"

}

variable "mapbox_access_token" {
  type        = string
  description = "Mapbox access token for the application"
}