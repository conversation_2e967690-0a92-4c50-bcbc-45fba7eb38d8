name: production deployments

on:
  push:
    branches:
      - main

env:
  AWS_REGION: "me-central-1"
  AWS_ACCOUNT_ID: "************"                     
  ECR_REPO_API: "rp-backend-service"                      
  ECR_REPO_WEB: "rp-frontend-service"              
  CLUSTER_NAME: rp-ecs-cluster-prod
  SERVICE_API_NAME: rp_backend_service_prod
  SERVICE_WEB_NAME: rp_frontend_service_prod

permissions:
  id-token: write
  contents: read

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    env:
      COMMIT_ID: ${{ github.sha }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          role-to-assume: arn:aws:iam::************:role/githubactions
          role-session-name: githubactions-session
          aws-region: ${{ env.AWS_REGION }}

      - name: Set ENV_NAME from branch
        run: echo "ENV_NAME=${GITHUB_REF##*/}" >> $GITHUB_ENV

      - name: Extract short Git commit SHA
        run: echo "SHORT_SHA=${GITHUB_SHA::7}" >> $GITHUB_ENV

      - name: Login to Amazon ECR
        run: |
          aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

      - name: Build and Push API Image
        run: |
          IMAGE_TAG=${SHORT_SHA}-api-${ENV_NAME}
          IMAGE_URI=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO_API}:${IMAGE_TAG}

          docker build -t $IMAGE_URI -f Dockerfile.api .
          docker push $IMAGE_URI

      - name: Build and Push Web Image
        run: |
          IMAGE_TAG=${SHORT_SHA}-web-${ENV_NAME}
          IMAGE_URI=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO_WEB}:${IMAGE_TAG}

          docker build -t $IMAGE_URI -f Dockerfile.web .
          docker push $IMAGE_URI

          # ecs deploying

      - name: Update ECS API service with new image
        run: |
          IMAGE_TAG=${SHORT_SHA}-api-${ENV_NAME}
          IMAGE_URI=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO_API}:${IMAGE_TAG}
      
          # Get current task definition ARN
          TASK_DEF_ARN=$(aws ecs describe-services \
            --cluster $CLUSTER_NAME \
            --services $SERVICE_API_NAME \
            --query "services[0].taskDefinition" \
            --output text)
      
          # Extract task definition name (without revision)
          TASK_DEF_NAME=$(basename $TASK_DEF_ARN | cut -d':' -f1)
      
          # Get full task definition JSON
          TASK_DEF_JSON=$(aws ecs describe-task-definition \
            --task-definition $TASK_DEF_NAME)
      
          # Create new task definition JSON with updated image
          NEW_TASK_DEF=$(echo "$TASK_DEF_JSON" | jq --arg IMAGE "$IMAGE_URI" '
            .taskDefinition |
            .containerDefinitions[0].image = $IMAGE |
            {
              family: .family,
              containerDefinitions: .containerDefinitions,
              executionRoleArn: .executionRoleArn,
              taskRoleArn: .taskRoleArn,
              networkMode: .networkMode,
              requiresCompatibilities: .requiresCompatibilities,
              cpu: .cpu,
              memory: .memory
            }')
      
          echo "$NEW_TASK_DEF" > new-api-task-def.json
      
          # Register new task definition
          NEW_TASK_DEF_ARN=$(aws ecs register-task-definition \
            --cli-input-json file://new-api-task-def.json \
            --query 'taskDefinition.taskDefinitionArn' \
            --output text)
      
          # Update ECS service with the new task definition
          aws ecs update-service \
            --cluster $CLUSTER_NAME \
            --service $SERVICE_API_NAME \
            --task-definition $NEW_TASK_DEF_ARN

      - name: Update ECS WEB service with new image
        run: |
          IMAGE_TAG=${SHORT_SHA}-web-${ENV_NAME}
          IMAGE_URI=${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO_WEB}:${IMAGE_TAG}
      
          # Get current task definition ARN
          TASK_DEF_ARN=$(aws ecs describe-services \
            --cluster $CLUSTER_NAME \
            --services $SERVICE_WEB_NAME \
            --query "services[0].taskDefinition" \
            --output text)
      
          # Extract task definition name (without revision)
          TASK_DEF_NAME=$(basename $TASK_DEF_ARN | cut -d':' -f1)
      
          # Get full task definition JSON
          TASK_DEF_JSON=$(aws ecs describe-task-definition \
            --task-definition $TASK_DEF_NAME)
      
          # Create new task definition JSON with updated image
          NEW_TASK_DEF=$(echo "$TASK_DEF_JSON" | jq --arg IMAGE "$IMAGE_URI" '
            .taskDefinition |
            .containerDefinitions[0].image = $IMAGE |
            {
              family: .family,
              containerDefinitions: .containerDefinitions,
              executionRoleArn: .executionRoleArn,
              taskRoleArn: .taskRoleArn,
              networkMode: .networkMode,
              requiresCompatibilities: .requiresCompatibilities,
              cpu: .cpu,
              memory: .memory
            }')
      
          echo "$NEW_TASK_DEF" > new-web-task-def.json
      
          # Register new task definition
          NEW_TASK_DEF_ARN=$(aws ecs register-task-definition \
            --cli-input-json file://new-web-task-def.json \
            --query 'taskDefinition.taskDefinitionArn' \
            --output text)
      
          # Update ECS service with the new task definition
          aws ecs update-service \
            --cluster $CLUSTER_NAME \
            --service $SERVICE_WEB_NAME \
            --task-definition $NEW_TASK_DEF_ARN
