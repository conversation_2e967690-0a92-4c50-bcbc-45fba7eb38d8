on:
  pull_request:
    branches: [main]
    types: [opened, reopened, synchronize, edited]

jobs:
  up:
    name: Deploy PR to ephemeral environment
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
      pull-requests: write

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Source Configuration
        working-directory: ./.github/workflows
        run: |
          # make input envvars available to all steps
          source config.env
          echo "AWS_REGION=${AWS_REGION}" >> $GITHUB_ENV
          echo "AWS_ROLE=${AWS_ROLE}" >> $GITHUB_ENV
          echo "TF_BACKEND_S3_BUCKET=${TF_BACKEND_S3_BUCKET}" >> $GITHUB_ENV
          environment=${GITHUB_HEAD_REF##*/}
          echo "ENVIRONMENT=${environment}" >> $GITHUB_ENV


      - name: Assume AWS IAM Role
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: ${{ env.AWS_ROLE }}

      - name: Install Terraform CLI
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.2.9
          terraform_wrapper: false

      - name: Lambda-ify
        run: |
          echo "COPY --from=public.ecr.aws/awsguru/aws-lambda-adapter:0.8.2 /lambda-adapter /opt/extensions/lambda-adapter" >> Dockerfile.api
          echo "COPY --from=public.ecr.aws/awsguru/aws-lambda-adapter:0.8.2 /lambda-adapter /opt/extensions/lambda-adapter" >> Dockerfile.web

      - name: Fetch SSM Parameters
        id: get-param
        run: |
          echo "RDS_PASSWORD=$(aws ssm get-parameter --name "/rp/backend/service/${{ env.ENVIRONMENT }}/db_password" --with-decryption --query "Parameter.Value" --output text)" >> $GITHUB_ENV
          echo "DB_HOST=$(aws ssm get-parameter --name "/rp/backend/service/${{ env.ENVIRONMENT }}/db_host" --with-decryption --query "Parameter.Value" --output text)" >> $GITHUB_ENV
          echo "REDIS_URL=$(aws ssm get-parameter --name "/rp/backend/service/${{ env.ENVIRONMENT }}/redis_url" --with-decryption --query "Parameter.Value" --output text)" >> $GITHUB_ENV
          echo "CLERK_PUBLISHABLE_KEY=$(aws ssm get-parameter --name "/rp/backend/service/${{ env.ENVIRONMENT }}/clerk_publishable_key" --with-decryption --query "Parameter.Value" --output text)" >> $GITHUB_ENV
          echo "CLERK_SECRET_KEY=$(aws ssm get-parameter --name "/rp/backend/service/${{ env.ENVIRONMENT }}/clerk_secret_key" --with-decryption --query "Parameter.Value" --output text)" >> $GITHUB_ENV
          echo "MAPBOX_ACCESS_TOKEN=$(aws ssm get-parameter --name "/rp/backend/service/${{ env.ENVIRONMENT }}/mapbox_access_token" --with-decryption --query "Parameter.Value" --output text)" >> $GITHUB_ENV

      - name: Deploy to Ephemeral Environment
        id: furl
        env:
          DOCKER_BUILDKIT: 1
        working-directory: ./.github/workflows
        run: |
          DOCKER_TAG=$(git rev-parse --short=7 HEAD)
          echo "Commit ID: $DOCKER_TAG"

          terraform init \
            -backend-config="bucket=${TF_BACKEND_S3_BUCKET}" \
            -backend-config="key=github-action/${ENVIRONMENT}.tfstate" \
            -backend-config="region=${AWS_REGION}"

          terraform apply -auto-approve \
            -var="name=${{ github.event.repository.name }}" \
            -var="environment=${ENVIRONMENT}" \
            -var="database_password=${RDS_PASSWORD}" \
            -var="database_host=${DB_HOST}" \
            -var="redis_url=${REDIS_URL}" \
            -var="cleark_publishable_key=${CLERK_PUBLISHABLE_KEY}" \
            -var="clear_secret_key=${CLERK_SECRET_KEY}" \
            -var="mapbox_access_token=${MAPBOX_ACCESS_TOKEN}" \
            -var="image_tag_api=${DOCKER_TAG}-api" \
            -var="image_tag_web=${DOCKER_TAG}-web"

          echo "Url_api=$(terraform output -json | jq '.endpoint_url_api.value' -r)" >> $GITHUB_OUTPUT
          echo "Url_web=$(terraform output -json | jq '.endpoint_url_web.value' -r)" >> $GITHUB_OUTPUT

      - name: Add HTTPS endpoint to PR comment
        uses: mshick/add-pr-comment@v1
        with:
          message: |
            :rocket: Code successfully deployed to a new ephemeral containerized PR environment!

            🌎 API URL:- ${{ steps.furl.outputs.Url_api }}
            
            🌎 WEB URL:- ${{ steps.furl.outputs.Url_web }}

          repo-token: ${{ secrets.GITHUB_TOKEN }}
          repo-token-user-login: "github-actions[bot]"
          allow-repeats: true


      #     # aws iam delete-role-policy --role-name prop-stack-monorepo-dev-api --policy-name prop-stack-monorepo-dev-api-logs
      #     # aws iam delete-role --role-name prop-stack-monorepo-dev-api

      #     # aws iam delete-role-policy --role-name prop-stack-monorepo-dev-web --policy-name prop-stack-monorepo-dev-web-logs
      #     # aws iam delete-role --role-name prop-stack-monorepo-dev-web