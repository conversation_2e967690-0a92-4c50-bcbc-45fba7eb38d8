# Version control
.git
.gitignore
.gitattributes
.github/

# Build artifacts
node_modules/
dist/
build/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn

# Development and environment files
# .env files are needed for Docker builds
# .env
# .env.*
# *.env.local
.editorconfig
.eslintrc
.prettierrc
.vscode/
.idea/
*.sublime-*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
README.md
CHANGELOG.md
docs/
*.md

# Testing
__tests__/
test/
tests/
coverage/
.nyc_output/

# Temporary files
tmp/
temp/
*.tmp
*.bak
*.swp

# Docker related
Dockerfile*
docker-compose*
.docker/

# Large media files (if applicable)
*.mp4
*.mov
*.avi
*.wmv
*.flv
*.iso
*.tar
*.zip
*.gz
*.tgz
*.rar
.next/
apps/web/.next/

Dockerfile
# Ignore all Dockerfiles everywhere
**/Dockerfile
**/Dockerfile.*
deploy.sh
cdn_assets/