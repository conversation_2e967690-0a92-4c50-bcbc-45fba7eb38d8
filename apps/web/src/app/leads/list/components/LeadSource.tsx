'use client';
import { AnimatePresence, motion } from 'motion/react';

import { useTRPC } from '../../../../utils/trpc';
import { useQuery } from '@tanstack/react-query';
import { Button } from '@rayna-prop4/ui/button';
import { useOutsideClick } from '@rayna-prop4/ui/hooks/use-outside-click';
import { useEffect, useRef, useState } from 'react';

const LeadSoruce = ({ leadId }: { leadId: string }) => {
  const trpc = useTRPC();
  const { data, isLoading, isError } = useQuery(
    trpc.tenantAdmin.getLeadGenerationByLeadId.queryOptions({
      leadId,
    }),
  );

  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function onKeyDown(event: KeyboardEvent) {
      if (event.key === 'Escape') {
        setIsDetailsOpen(false);
      }
    }

    if (isDetailsOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [isDetailsOpen]);

  useOutsideClick(ref, () => setIsDetailsOpen(false));

  if (isLoading) {
    return null; // Display nothing while loading
  }

  if (isError) {
    return <div>error while fetching data</div>;
  }

  if (!data) {
    return <div className="container py-10"></div>;
  }

  const briefItems = Object.entries(data.details)
    .filter(([_, value]) => value !== null) // Remove null values
    .map(([key, value]) => {
      const mapping = data.fields.find((m) => m?.fieldReference === key);
      const title = (mapping ? mapping.name : key)
        .replace(/([A-Z])/g, ' $1')
        .trim();
      return { title, description: String(value) };
    });

  return (
    <>
      <AnimatePresence>
        {isDetailsOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/20 h-full w-full z-10"
          />
        )}
      </AnimatePresence>
      <AnimatePresence>
        {isDetailsOpen && (
          <div className="fixed inset-0 grid place-items-center z-[100] p-4">
            <motion.button
              key="close-button"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.5 }}
              transition={{ duration: 0.3 }}
              className="flex absolute top-6 right-6 items-center justify-center bg-white rounded-full h-8 w-8 z-[101]"
              onClick={() => setIsDetailsOpen(false)}
            >
              <CloseIcon />
            </motion.button>
            <motion.div
              layoutId="details-card"
              ref={ref}
              initial={{ opacity: 0, scale: 0.75, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.75, y: 20 }}
              transition={{ duration: 0.3 }}
              className="w-full max-w-[500px] h-[90vh] flex flex-col bg-white dark:bg-neutral-900 rounded-3xl overflow-hidden"
            >
              <div className="p-4 border-b">
                <motion.h2
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1, duration: 0.3 }}
                  className="text-xl font-bold"
                >
                  All Details
                </motion.h2>
              </div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.3 }}
                className="flex-1 overflow-y-auto p-4"
              >
                {briefItems.map((item, index) => (
                  <motion.div
                    key={item.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 * (index + 3), duration: 0.3 }}
                    className="mb-4"
                  >
                    <h3 className="font-bold text-neutral-700 dark:text-neutral-200 capitalize">
                      {item.title}
                    </h3>
                    <p className="text-neutral-600 dark:text-neutral-400">
                      {item.description}
                    </p>
                  </motion.div>
                ))}
              </motion.div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
      <div className="max-w-2xl mx-auto w-full">
        <Button onClick={() => setIsDetailsOpen(true)}>View</Button>
      </div>
    </>
  );
};

export const CloseIcon = () => {
  return (
    <motion.svg
      initial={{
        opacity: 0,
      }}
      animate={{
        opacity: 1,
      }}
      exit={{
        opacity: 0,
        transition: {
          duration: 0.05,
        },
      }}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="h-4 w-4 text-black"
    >
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M18 6l-12 12" />
      <path d="M6 6l12 12" />
    </motion.svg>
  );
};

export default LeadSoruce;
