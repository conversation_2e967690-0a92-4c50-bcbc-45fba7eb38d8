'use client';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  schemaCreateExpenseAccount,
  typeCreateExpenseAccount,
} from '@rayna-prop4/zod-schema';
import React from 'react';
import { useForm } from 'react-hook-form';
import { Input } from '@rayna-prop4/ui/input';
import { Button } from '@rayna-prop4/ui/button';
import { useTRPC } from './../../../../utils/trpc';
import { useMutation } from '@tanstack/react-query';
import { useToast } from '@rayna-prop4/ui/hooks/use-toast';
import { useRouter } from 'next/navigation';

const AddExpenseAccountPage = () => {
  const form = useForm<typeCreateExpenseAccount>({
    resolver: zodResolver(schemaCreateExpenseAccount),
    defaultValues: {
      name: '',
      description: '',
      initialBalance: 0,
    },
  });
  const router = useRouter();
  const trpc = useTRPC();
  const { toast } = useToast();

  const createFinancialAccount = useMutation(
    trpc.tenantAdmin.createExpenseAccount.mutationOptions({
      onSuccess: () => {
        toast({
          title: 'Success',
          description: 'Financial account created successfully',
        });
        router.replace('/settings/expense-accounts');
      },
      onError: (error) => {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      },
    }),
  );

  const onSubmit = (data: typeCreateExpenseAccount) => {
    createFinancialAccount.mutate(data);
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">Add Expense Account</h1>
        <p className="mt-2">
          Create a new expense account by filling out the form below.
        </p>
      </div>

      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-2">
          <label htmlFor="name" className="block text-sm font-medium">
            Account Name
          </label>
          <Input
            id="name"
            placeholder="Enter account name"
            className="w-full"
            {...form.register('name')}
          />
          {form.formState.errors.name && (
            <p className="text-sm text-red-500">
              {form.formState.errors.name.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="description" className="block text-sm font-medium">
            Description
          </label>
          <Input
            id="description"
            placeholder="Enter account description"
            className="w-full"
            {...form.register('description')}
          />
          {form.formState.errors.description && (
            <p className="text-sm text-red-500">
              {form.formState.errors.description.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="initialBalance" className="block text-sm font-medium">
            Initial Balance
          </label>
          <Input
            id="initialBalance"
            type="number"
            placeholder="0.00"
            className="w-full"
            {...form.register('initialBalance')}
          />
          {form.formState.errors.initialBalance && (
            <p className="text-sm text-red-500">
              {form.formState.errors.initialBalance.message}
            </p>
          )}
        </div>

        <div className="flex gap-4 pt-4">
          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={createFinancialAccount.isPending}
          >
            {createFinancialAccount.isPending
              ? 'Creating...'
              : 'Create Account'}
          </Button>
          <Button
            type="button"
            variant="outline"
            className="w-full sm:w-auto"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AddExpenseAccountPage;
