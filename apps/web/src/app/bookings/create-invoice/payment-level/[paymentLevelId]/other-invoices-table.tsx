'use client';

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@rayna-prop4/ui/table';
import dayjs from 'dayjs';
import { Invoice } from './types';

interface OtherInvoicesTableProps {
  invoices: Invoice[];
}

export const OtherInvoicesTable: React.FC<OtherInvoicesTableProps> = ({
  invoices,
}) => {
  if (invoices.length === 0) return null;

  return (
    <div>
      <h2 className="text-lg font-semibold mb-4">Other Invoices</h2>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Invoice ID</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Created Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Last Updated</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {invoices.map((invoice) => (
            <TableRow key={invoice.id}>
              <TableCell>{invoice.id}</TableCell>
              <TableCell>{invoice.amount}</TableCell>
              <TableCell>
                {dayjs(invoice.createdAt).format('DD MMM YYYY')}
              </TableCell>
              <TableCell>{invoice.status}</TableCell>
              <TableCell>
                {dayjs(invoice.updatedAt).format('DD MMM YYYY')}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
