import { z } from 'zod';

const baseSchema = z.record(
  z.string(),
  z.object({
    companyCommission: z.object({
      percentage: z.coerce.number().min(0).max(10),
      amount: z.coerce.number().min(0),
    }),
    expenses: z.record(
      z.string(),
      z.object({
        amount: z.coerce.number().min(0),
        percentage: z.coerce.number().min(0).max(20),
      }),
    ),
    executionCommission: z.object({
      percentage: z.coerce.number().min(0).max(80),
    }),
    grossProfit: z.object({
      amount: z.coerce.number().min(0),
    }),
    consultantCommission: z.record(
      z.string(),
      z.object({
        amount: z.coerce.number().min(0),
        percentage: z.coerce.number().min(0).max(100),
      }),
    ),
  }),
);

// Enhanced schema with custom validation
const zodSchema = baseSchema.refine(
  (data) => {
    // For each record in the outer object
    for (const key in data) {
      const record = data[key];

      // Calculate the sum of all expense amounts
      const expensesSum = Object.values(record.expenses).reduce(
        (sum, expense) => sum + expense.amount,
        0,
      );

      // Get the gross profit amount
      const grossProfitAmount = record.grossProfit.amount;

      // Calculate the sum of all consultant commission amounts
      const consultantCommissionsSum = Object.values(
        record.consultantCommission,
      ).reduce((sum, commission) => sum + commission.amount, 0);

      // Calculate the expected company commission amount
      const expectedCompanyCommission =
        expensesSum + grossProfitAmount + consultantCommissionsSum;

      // Compare with the actual company commission amount
      if (record.companyCommission.amount !== expectedCompanyCommission) {
        return false; // Validation fails if they're not equal
      }
    }

    return true; // Validation passes if all records match the expected sum
  },
  {
    message:
      'Company commission amount must equal the sum of all expenses, gross profit, and consultant commissions',
  },
);

export { zodSchema, baseSchema };
