//@ts-check

const { composePlugins, withNx } = require('@nx/next');

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const defaultConfig = {
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  output: 'standalone', // Required for AWS Lambda deployment
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'i.pravatar.cc',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

const { PHASE_DEVELOPMENT_SERVER } = require('next/constants');

module.exports = async (phase, context) => {
  let updatedConfig = plugins.reduce((acc, fn) => fn(acc), defaultConfig);

  // Apply the async function that `withNx` returns.
  updatedConfig = await withNx(updatedConfig)(phase, context);

  if (phase === PHASE_DEVELOPMENT_SERVER) {
    return {
      ...updatedConfig,
    };
  }

  return {
    ...updatedConfig,
    assetPrefix: process.env.NEXT_PUBLIC_ASSET_PREFIX,
  };
};
