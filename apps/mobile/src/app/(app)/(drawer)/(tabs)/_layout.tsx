import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { Redirect, Tabs } from 'expo-router';
import { useCallback } from 'react';
import { StyleSheet, Text } from 'react-native';

import { Icons } from '../../../../components/Icons';
import { SignedIn, SignedOut } from '@clerk/clerk-expo';

const TabBarLabel = ({
  focused,
  title,
}: {
  focused: boolean;
  title: string;
}) => (
  <Text style={[styles.tabBar, focused ? styles.tabBarActive : {}]}>
    {title}
  </Text>
);

const DashboardIcon = ({ focused }: { focused: boolean }) => (
  <Icons.dashboard
    fill={focused ? '#66a2a2' : 'black'}
    stroke={focused ? '#66a2a2' : 'black'}
  />
);

const ActiveLeadsIcon = ({ focused }: { focused: boolean }) => (
  <Icons.activeLeads
    fill={focused ? '#66a2a2' : 'black'}
    stroke={focused ? '#66a2a2' : 'black'}
  />
);

const CalendarIcon = ({ focused }: { focused: boolean }) => (
  <Icons.calender fill={focused ? '#66a2a2' : 'black'} />
);

const TransactionIcon = ({ focused }: { focused: boolean }) => (
  <Icons.transaction fill={focused ? '#66a2a2' : 'black'} />
);

export default function TabLayout() {
  const renderDashboardLabel = useCallback(
    ({ focused }: { focused: boolean }) => (
      <TabBarLabel focused={focused} title="Dashboard" />
    ),
    [],
  );

  const renderDashboardIcon = useCallback(
    ({ focused }: { focused: boolean }) => <DashboardIcon focused={focused} />,
    [],
  );

  const renderActiveLeadsLabel = useCallback(
    ({ focused }: { focused: boolean }) => (
      <TabBarLabel focused={focused} title="Active Leads" />
    ),
    [],
  );

  const renderActiveLeadsIcon = useCallback(
    ({ focused }: { focused: boolean }) => (
      <ActiveLeadsIcon focused={focused} />
    ),
    [],
  );

  const renderCalendarLabel = useCallback(
    ({ focused }: { focused: boolean }) => (
      <TabBarLabel focused={focused} title="Calendar" />
    ),
    [],
  );

  const renderCalendarIcon = useCallback(
    ({ focused }: { focused: boolean }) => <CalendarIcon focused={focused} />,
    [],
  );

  const renderBookingsLabel = useCallback(
    ({ focused }: { focused: boolean }) => (
      <TabBarLabel focused={focused} title="Bookings" />
    ),
    [],
  );

  const renderBokingsIcon = useCallback(
    ({ focused }: { focused: boolean }) => (
      <TransactionIcon focused={focused} />
    ),
    [],
  );
  return (
    <>
      <SignedIn>
        <BottomSheetModalProvider>
          <Tabs screenOptions={{ headerShown: false }}>
            <Tabs.Screen
              name="home"
              options={{
                tabBarLabel: renderDashboardLabel,
                tabBarIcon: renderDashboardIcon,
              }}
            />
            <Tabs.Screen
              name="active-leads"
              options={{
                tabBarLabel: renderActiveLeadsLabel,
                tabBarIcon: renderActiveLeadsIcon,
              }}
            />
            <Tabs.Screen
              name="calender/index"
              options={{
                tabBarLabel: renderCalendarLabel,
                tabBarIcon: renderCalendarIcon,
              }}
            />
            <Tabs.Screen
              name="bookings"
              options={{
                tabBarLabel: renderBookingsLabel,
                tabBarIcon: renderBokingsIcon,
              }}
            />
          </Tabs>
        </BottomSheetModalProvider>
      </SignedIn>
      <SignedOut>
        <Redirect href="/sign-in" />
      </SignedOut>
    </>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    color: 'black',
    fontSize: 12,
    fontFamily: 'Inter_400Regular',
  },
  tabBarActive: {
    color: '#66a2a2',
  },
});
