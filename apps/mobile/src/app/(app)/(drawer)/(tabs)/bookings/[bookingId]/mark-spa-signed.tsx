import { Link, router, useLocalSearchParams, useRouter } from 'expo-router';
import { ActivityIndicator, Text, View } from 'react-native';

import { Icons } from '../../../../../../components/Icons';
import { Button } from '../../../../../../components/button';
import { useTRPC } from '../../../../../../components/trpc';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import Toast from 'react-native-toast-message';

const MarkSpaSignedPage = () => {
  const { bookingId } = useLocalSearchParams() as { bookingId: string };
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const router = useRouter();

  const { mutate: markSpaSigned, isPending } = useMutation(
    trpc.consultantBooking.markSpaSigned.mutationOptions({
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: [
            ['consultant', 'getIncompleteBookings'],
            { type: 'query' },
          ],
        });
        router.push(`/(app)/(drawer)/(tabs)/bookings/`);
      },
      onError: (error) => {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: JSON.stringify(error),
        });
      },
    }),
  );

  const handleMarkSpaSigned = () => {
    markSpaSigned({ bookingId });
  };

  return (
    <View className="rounded-t-[28px] bg-background">
      <Text className="ml-6 mt-8 font-bold text-title">Mark Spa Signed!</Text>
      <Text className="ml-6 mt-2 text-b1">
        Do you want to mark the SPA as signed?
      </Text>

      <View className="mt-8"></View>
      <View className="h-48 ">
        <View className="absolute bottom-32 right-8 flex-row gap-4">
          <Link dismissTo href={`/(app)/(drawer)/(tabs)/bookings/`} asChild>
            <Button intent="secondary">Cancel</Button>
          </Link>
          <Button
            intent="callToAction"
            style={{
              width: 100,
            }}
            disabled={isPending}
            onPress={handleMarkSpaSigned}
          >
            {isPending ? <ActivityIndicator /> : 'Yes'}
          </Button>
        </View>
      </View>
    </View>
  );
};

export default MarkSpaSignedPage;
