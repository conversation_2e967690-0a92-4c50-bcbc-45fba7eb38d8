import { Link, useLocalSearchParams } from 'expo-router';
import { Pressable, ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import React from 'react';

import BackButton from '../../../../../../../../components/back-button';
import { useTRPC } from '../../../../../../../../components/trpc';
import { StageCard } from '../../../../../../../../components/stage-card';
import { useQuery } from '@tanstack/react-query';
import useIsFocused from '../../../../../../../../hooks/useIsFocussed';
import LoadingScreen from './../../../../../../../../components/loading-screen';

const LeadStageDetailsPage = () => {
  const { isFocussed } = useIsFocused();

  const { leadId, leadWorkflowId } = useLocalSearchParams() as {
    leadId: string;
    leadWorkflowId: string;
  };

  const trpc = useTRPC();

  const {
    data: leadStageDetails,
    isLoading: isLeadStageDetailsLoading,
    isRefetching: isLeadStageDetailsRefetching,
  } = useQuery(
    trpc.consultantLeads.getLeadStageDetails.queryOptions(
      {
        leadId,
        leadWorkflowId,
      },
      {
        staleTime: 10000,
      },
    ),
  );

  if (isLeadStageDetailsLoading || isLeadStageDetailsRefetching) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <View className="flex-row justify-between px-8">
        <BackButton />
        <Link
          href={`/(app)/(drawer)/(tabs)/active-leads/stages/${leadId}/${leadWorkflowId}/profile`}
          asChild
        >
          <Pressable>
            <Text className="font-semibold text-primary-600">View Profile</Text>
          </Pressable>
        </Link>
      </View>
      <ScrollView className="px-4">
        <View className="pt-5 px-4">
          <Text className="font-semibold text-title capitalize">
            {`${leadStageDetails?.lead?.clientName}`}
          </Text>
          <View className="mt-3 flex-row">
            <Text className="flex-1 font-semibold">
              LEAD ID : {leadId.slice(-4)}
            </Text>
            <Text className="flex-1 font-semibold text-primary-600 text-right">
              MOVE TO LOST LEADS
            </Text>
          </View>
        </View>

        <View className="mx-2 my-4">
          {leadStageDetails?.stageStatus.map((stage) => {
            return (
              <Link
                key={stage.sequence}
                href={`/(app)/(drawer)/(tabs)/active-leads/stages/${leadId}/${leadWorkflowId}/${stage.sequence}/${stage.route}`}
                disabled={!stage.canRoute}
                className="w-full"
                asChild
              >
                <Pressable>
                  <StageCard
                    key={stage.sequence}
                    stageName={stage.name}
                    status={stage.status as 'pending' | 'completed' | 'active'}
                    stageNum={stage.sequence.toString()}
                  />
                </Pressable>
              </Link>
            );
          })}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default LeadStageDetailsPage;
