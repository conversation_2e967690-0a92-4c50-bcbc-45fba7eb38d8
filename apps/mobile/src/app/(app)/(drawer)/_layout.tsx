import { Icons } from '../../../components/Icons';
import { Drawer } from 'expo-router/drawer';
import { Image, Text, View } from 'react-native';
import { DrawerToggleButton } from '@react-navigation/drawer';
import {
  DrawerItemList,
  DrawerContentScrollView,
} from '@react-navigation/drawer';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const CustomDrawerContent = (props: any) => {
  const insets = useSafeAreaInsets();

  return (
    <DrawerContentScrollView
      {...props}
      contentContainerStyle={{
        flex: 1,
        backgroundColor: '#5484a9',
      }}
    >
      <View
        style={{
          backgroundColor: '#5484a9', // primary-600 color from tailwind config
          paddingBottom: 20,
          paddingHorizontal: 20,
        }}
      >
        <View className="flex flex-row items-center py-4">
          <View className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
            <Icons.purse size={24} color="#5484a9" />
          </View>
          <View className="ml-3">
            <Text className="text-white font-semibold text-xl">
              Rayna Properties
            </Text>
            <Text className="text-white opacity-80">Lead Management</Text>
          </View>
        </View>
      </View>

      <View className="flex-1 px-2 py-3">
        <DrawerItemList
          {...props}
          activeTintColor="#5484a9"
          inactiveTintColor="#2c485d"
          activeBackgroundColor="#f1f5f8"
          itemStyle={{
            borderRadius: 8,
            paddingLeft: 8,
            marginBottom: 5,
          }}
          labelStyle={{
            fontFamily: 'Inter_600SemiBold',
            marginLeft: -16,
          }}
        />
      </View>
    </DrawerContentScrollView>
  );
};

export default function Layout() {
  return (
    <Drawer
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          width: '75%',
        },
        drawerActiveBackgroundColor: '#f1f5f8', // primary-100 from tailwind
        drawerActiveTintColor: '#5484a9', // primary-600 from tailwind
        drawerInactiveTintColor: '#2c485d', // primary-900 from tailwind
      }}
      initialRouteName="(tabs)"
      drawerContent={(props) => <CustomDrawerContent {...props} />}
    >
      <Drawer.Screen
        name="(tabs)"
        options={{
          drawerLabel: 'Home',
          title: 'Home',
          drawerIcon: ({ color }) => (
            <Icons.dashboard fill={color} stroke={color} />
          ),
        }}
      />
      <Drawer.Screen
        name="(wallet_tabs)"
        options={{
          drawerLabel: 'Wallet',
          title: 'Wallet',
          drawerIcon: ({ color }) => <Icons.purse color={color} />,
        }}
      />
    </Drawer>
  );
}
