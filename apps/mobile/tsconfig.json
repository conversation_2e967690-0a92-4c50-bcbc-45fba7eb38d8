{"extends": "../../tsconfig.base.json", "compilerOptions": {"allowSyntheticDefaultImports": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleResolution": "node", "skipLibCheck": true, "resolveJsonModule": true, "strict": true, "declaration": true}, "files": [], "include": ["nativewind-env.d.ts", ".expo/types/**/*.ts", "expo-env.d.ts"], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}]}