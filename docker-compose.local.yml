services:
  postgres:
    image: postgres:16-alpine
    container_name: rayna_properties_db
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: rayna_properties
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:8.0-M03-alpine
    container_name: rayna_properties_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  api:
    platform: linux/amd64
    build: 
      context: .
      dockerfile: Dockerfile.api
    container_name: rayna_properties_api
    image: local-api
    ports:
      - "4000:4000"
    # environment:
    #   NODE_ENV: production

    #   DATABASE_URL: ************************************************/rayna_properties
    #   REDIS_URL: redis://redisssssss:6379

    #   CLERK_SECRET_KEY: sk_test_g6sZbJdsoXCJMjpqWGTHkANgetRB6s47gx8A6vCNUY
    #   CLERK_PUBLISHABLE_KEY: pk_test_ZWxlY3RyaWMtcHJhd24tMi5jbGVyay5hY2NvdW50cy5kZXYk
    #   CLERK_DEBUG: 'true'
    #   AWS_ACCESS_KEY_ID: ********************
    #   AWS_SECRET_ACCESS_KEY: kZSQZWSszlVVjQl1xc9RSvsVHrgrGy8kRHQClk1a
    #   AWS_REGION: me-central-1
    #   AWS_BUCKET_NAME: prop-stack-local
    #   TRPC_PATH: api/trpc
    #   API_PORT: 4000
    #   MAPBOX_ACCESS_TOKEN: *************************************************************************************************

    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy

  web:
    build:
      context: .
      dockerfile: Dockerfile.web
      args:
        - NEXT_PUBLIC_API_URL=http://localhost:4000/api/trpc
        - NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_ZWxlY3RyaWMtcHJhd24tMi5jbGVyay5hY2NvdW50cy5kZXYk
        - API_URL=http://localhost:4000/api/trpc
        - REDIS_URL=redis://redis:6379
        - CLERK_SECRET_KEY=sk_test_g6sZbJdsoXCJMjpqWGTHkANgetRB6s47gx8A6vCNUY
    container_name: rayna_properties_web
    image: local-web
    platform: linux/amd64
    ports:
      - "3000:3000"
    environment:
      NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: pk_test_ZWxlY3RyaWMtcHJhd24tMi5jbGVyay5hY2NvdW50cy5kZXYk
      CLERK_SECRET_KEY: sk_test_g6sZbJdsoXCJMjpqWGTHkANgetRB6s47gx8A6vCNUY
      API_URL: http://api:4000/api/trpc
      NEXT_PUBLIC_API_URL: http://localhost:4000/api/trpc,
      REDIS_URL: redis://redis:6379

    depends_on:
      - api

volumes:
  postgres_data:
  redis_data: 
