# Dependencies stage
FROM node:22-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
WORKDIR /app
COPY package*.json ./
COPY pnpm-lock.yaml ./
#COPY .env ./

# Production dependencies
FROM base AS prod-deps
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --prod --frozen-lockfile --ignore-scripts

# Build stage
FROM base AS build
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
WORKDIR /app
#COPY .env ./
COPY nx.json ./
COPY tsconfig.base.json ./
COPY tsconfig.json ./
COPY jest.config.ts ./
COPY jest.preset.js ./
COPY eslint.config.mjs ./
COPY babel.config.json ./
COPY components.json ./
COPY libs/ libs/
COPY apps/api/ apps/api/
RUN pnpm nx build api

# Production stage
FROM base
WORKDIR /app
#COPY .env ./
COPY --from=prod-deps /app/node_modules ./node_modules
# Copy the build output directory structure properly
COPY --from=build /app/dist/apps/api/ ./
ENV PORT=4000
EXPOSE ${PORT}
# Actually run the Node.js application
CMD ["node", "main.js"]