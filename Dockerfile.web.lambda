# Dependencies stage
FROM node:22-slim AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
WORKDIR /app
COPY package*.json ./
COPY pnpm-lock.yaml ./

# Production dependencies stage
FROM base AS prod-deps
RUN pnpm install --prod --frozen-lockfile --ignore-scripts

# Build stage
FROM base AS build
RUN pnpm install --frozen-lockfile
WORKDIR /app
COPY nx.json ./
COPY tsconfig.base.json ./
COPY tsconfig.json ./
COPY jest.config.ts ./
COPY jest.preset.js ./
COPY eslint.config.mjs ./
COPY babel.config.json ./
COPY components.json ./
COPY libs/ libs/
COPY apps/web/ apps/web/

ARG NEXT_PUBLIC_API_URL=""
ARG NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="pk_test_aW4taHVza3ktNS5jbGVyay5hY2NvdW50cy5kZXYk"
ARG API_URL=""
ARG REDIS_URL=""
ARG CLERK_SECRET_KEY="sk_test_lhl6aAlB7oKo7CBdyWKzLjeihMeh6KB4dtAyAI0Wif"
ARG NEXT_PUBLIC_ASSET_PREFIX=""
ARG ASSET_VERSION="latest"
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY}
ENV API_URL=${API_URL}
ENV REDIS_URL=${REDIS_URL}
ENV CLERK_SECRET_KEY=${CLERK_SECRET_KEY}
ENV NEXT_PUBLIC_ASSET_PREFIX=${NEXT_PUBLIC_ASSET_PREFIX}/${ASSET_VERSION}

ENV AWS_LWA_LOG_LEVEL=DEBUG
ENV AWS_LWA_ASYNC_INIT=true

RUN echo "The value is: $NEXT_PUBLIC_API_URL"
RUN echo "The value is: $NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY"
RUN echo "The value is: $API_URL"
RUN echo "The value is: $REDIS_URL"
RUN echo "The value is: $NEXT_PUBLIC_ASSET_PREFIX"

RUN NEXT_PUBLIC_API_URL=$NEXT_PUBLIC_API_URL \
    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY \
    CLERK_SECRET_KEY=$CLERK_SECRET_KEY \
    API_URL=$API_URL \
    REDIS_URL=$REDIS_URL \
    NEXT_PUBLIC_ASSET_PREFIX=$NEXT_PUBLIC_ASSET_PREFIX \
    pnpm nx build web --skip-nx-cache

# Final stage
FROM base
WORKDIR /app
# COPY --from=prod-deps /app/node_modules ./node_modules
COPY --from=build /app/apps/web/.next/standalone ./
COPY --from=build /app/apps/web/.next/static ./apps/web/.next/static
COPY --from=build /app/apps/web/public ./apps/web/public

EXPOSE 8080
CMD ["node", "apps/web/server.js"]